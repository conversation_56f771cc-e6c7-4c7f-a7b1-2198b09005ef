<template>
  <div class="weather-widget">
    <!-- 第一行：天气图标、天气和温度、地点 -->
    <div class="weather-row-1">
      <div class="weather-icon-col">
        <Icon :icon="getWeatherIcon(weatherData?.current.weather || '')" />
      </div>
      <div class="weather-info-col">
        <div class="temperature">{{ weatherData?.current.temperature }}°C</div>
        <div class="weather-desc">{{ weatherData?.current.weather }}</div>
      </div>
      <div class="location-col">
        <Icon icon="mdi:map-marker" class="location-icon" />
        {{ weatherData?.location }}
      </div>
    </div>

    <!-- 第二行：风向、湿度、气压 -->
    <div class="weather-row-2">
      <div class="detail-item">
        <span class="label">风向</span>
        <span class="value">{{ weatherData?.current.windDirection }}</span>
      </div>
      <div class="detail-item">
        <span class="label">湿度</span>
        <span class="value">{{ weatherData?.current.humidity }}%</span>
      </div>
      <div class="detail-item">
        <span class="label">气压</span>
        <span class="value">{{ weatherData?.current.pressure }}hPa</span>
      </div>
    </div>

    <!-- 第三行：未来3天天气水平排列 -->
    <div class="weather-row-3">
      <div
        v-for="(day, index) in weatherData?.forecast"
        :key="index"
        class="forecast-item"
      >
        <div class="forecast-date">{{ day.date }}</div>

        <div class="forecast-weather">
          <div class="forecast-icon">
            <Icon :icon="getWeatherIcon(day.weather)" />
          </div>
          {{ day.weather }}
        </div>
        <div class="forecast-temp">
          <span class="high">{{ day.high }}°</span>
          <span class="low">{{ day.low }}°</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import dayjs from "dayjs";
import { Icon } from "@iconify/vue";

interface WeatherData {
  location: string;
  current: {
    temperature: number;
    weather: string;
    windDirection: string;
    windSpeed: number;
    pressure: number;
    humidity: number;
  };
  forecast: Array<{
    date: string;
    weather: string;
    high: number;
    low: number;
  }>;
}

const weatherData = ref<WeatherData | null>(null);
const loading = ref(true);

// 获取天气图标
const getWeatherIcon = (weather: string) => {
  const iconMap: Record<string, string> = {
    晴: "mdi:weather-sunny",
    多云: "mdi:weather-partly-cloudy",
    阴: "mdi:weather-cloudy",
    小雨: "mdi:weather-rainy",
    中雨: "mdi:weather-pouring",
    大雨: "mdi:weather-lightning-rainy",
    雷阵雨: "mdi:weather-lightning",
    雪: "mdi:weather-snowy",
    雾: "mdi:weather-fog",
  };

  for (const key in iconMap) {
    if (weather.includes(key)) {
      return iconMap[key];
    }
  }

  return "mdi:weather-partly-cloudy";
};

// 获取真实天气数据
const fetchWeatherData = async () => {
  try {
    loading.value = true;

    // 这里使用模拟数据，实际项目中可以接入真实的天气API
    // 比如和风天气、OpenWeatherMap等
    // await new Promise((resolve) => setTimeout(resolve, 1000)); // 模拟API请求延迟

    const mockWeatherData: WeatherData = {
      location: "广西北海",
      current: {
        temperature: 28,
        weather: "多云",
        windDirection: "东南风",
        windSpeed: 12,
        pressure: 1013,
        humidity: 75,
      },
      forecast: [
        {
          date: dayjs().add(1, "day").format("MM-DD"),
          weather: "晴",
          high: 30,
          low: 22,
        },
        {
          date: dayjs().add(2, "day").format("MM-DD"),
          weather: "小雨",
          high: 26,
          low: 20,
        },
        {
          date: dayjs().add(3, "day").format("MM-DD"),
          weather: "多云",
          high: 29,
          low: 23,
        },
      ],
    };

    weatherData.value = mockWeatherData;
  } catch (error) {
    console.error("获取天气数据失败:", error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchWeatherData();

  // 每小时更新一次天气数据
  setInterval(fetchWeatherData, 60 * 60 * 1000);
});
</script>

<style scoped lang="scss">
.weather-widget {
  display: flex;
  flex-direction: column;
  gap: 12px;
  font-size: 14px;
}

.loading,
.error {
  text-align: center;
  color: #0efcff;
  padding: 10px;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 第一行：天气图标、天气和温度、地点 - 3列布局
.weather-row-1 {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  font-size: 16px;

  .weather-icon-col {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;

    svg {
      font-size: 60px;
      color: #0efcff;
    }
  }

  .weather-info-col {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 4px;

    .temperature {
      font-weight: bold;
      color: #fff;
      line-height: 1;
    }

    .weather-desc {
      color: #ccc;
      line-height: 1;
    }
  }

  .location-col {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    color: #0efcff;
  }
}

// 第二行：风向、湿度、气压
.weather-row-2 {
  display: flex;
  justify-content: space-between;
  gap: 8px;

  .detail-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3px;

    .label {
      color: #ccc;
      line-height: 1;
    }

    .value {
      color: #fff;
      line-height: 1;
      font-weight: 500;
    }
  }
}

// 第三行：未来3天天气水平排列
.weather-row-3 {
  display: flex;
  gap: 6px;

  .forecast-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    padding: 6px 4px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    border: 1px solid rgba(0, 212, 255, 0.2);
    transition: all 0.2s ease;

    &:hover {
      background: rgba(0, 212, 255, 0.1);
      border-color: rgba(0, 212, 255, 0.4);
    }

    .forecast-date {
      color: #ccc;
      line-height: 1;
      font-weight: 500;
    }

    .forecast-icon {
      font-size: 18px;
      color: #00d4ff;
      margin: 2px 0;
    }

    .forecast-weather {
      color: #fff;
      text-align: center;
      line-height: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
    }

    .forecast-temp {
      display: flex;
      gap: 3px;
      font-weight: 500;

      .high {
        color: #ff6b6b;
      }

      .low {
        color: #74c0fc;
      }
    }
  }
}
</style>
